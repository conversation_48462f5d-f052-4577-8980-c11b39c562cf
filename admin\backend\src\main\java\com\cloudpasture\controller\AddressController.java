package com.cloudpasture.controller;

import com.cloudpasture.common.Result;
import com.cloudpasture.dto.AddressDTO;
import com.cloudpasture.service.AddressService;
import com.cloudpasture.service.AuthService;
import com.cloudpasture.vo.AddressVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户地址控制器
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@RestController
@RequestMapping("/address")
@Api(tags = "用户地址管理")
public class AddressController {

    @Autowired
    private AddressService addressService;

    @Autowired
    private AuthService authService;

    @PostMapping("/create")
    @ApiOperation("创建地址")
    public Result<AddressVO> createAddress(
            @RequestHeader("Authorization") String token,
            @RequestBody @Valid AddressDTO addressDTO) {
        // 移除Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        Long userId = authService.getUserIdFromToken(token);
        AddressVO addressVO = addressService.createAddress(userId, addressDTO);
        return Result.success(addressVO);
    }

    @PutMapping("/{id}")
    @ApiOperation("更新地址")
    public Result<AddressVO> updateAddress(
            @RequestHeader("Authorization") String token,
            @PathVariable Long id,
            @RequestBody @Valid AddressDTO addressDTO) {
        // 移除Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        Long userId = authService.getUserIdFromToken(token);
        AddressVO addressVO = addressService.updateAddress(userId, id, addressDTO);
        return Result.success(addressVO);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除地址")
    public Result<Void> deleteAddress(
            @RequestHeader("Authorization") String token,
            @PathVariable Long id) {
        // 移除Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        Long userId = authService.getUserIdFromToken(token);
        boolean success = addressService.deleteAddress(userId, id);
        return success ? Result.success() : Result.error("删除失败");
    }

    @GetMapping("/list")
    @ApiOperation("获取用户地址列表")
    public Result<List<AddressVO>> getUserAddresses(@RequestHeader("Authorization") String token) {
        // 移除Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        Long userId = authService.getUserIdFromToken(token);
        List<AddressVO> addresses = addressService.getUserAddresses(userId);
        return Result.success(addresses);
    }

    @GetMapping("/default")
    @ApiOperation("获取默认地址")
    public Result<AddressVO> getDefaultAddress(@RequestHeader("Authorization") String token) {
        // 移除Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        Long userId = authService.getUserIdFromToken(token);
        AddressVO address = addressService.getDefaultAddress(userId);
        return Result.success(address);
    }

    @PutMapping("/{id}/default")
    @ApiOperation("设置默认地址")
    public Result<Void> setDefaultAddress(
            @RequestHeader("Authorization") String token,
            @PathVariable Long id) {
        // 移除Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        Long userId = authService.getUserIdFromToken(token);
        boolean success = addressService.setDefaultAddress(userId, id);
        return success ? Result.success() : Result.error("设置失败");
    }

    @GetMapping("/{id}")
    @ApiOperation("获取地址详情")
    public Result<AddressVO> getAddressById(
            @RequestHeader("Authorization") String token,
            @PathVariable Long id) {
        // 移除Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        Long userId = authService.getUserIdFromToken(token);
        AddressVO address = addressService.getAddressById(userId, id);
        return Result.success(address);
    }
}
