package com.cloudpasture.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudpasture.common.BusinessException;
import com.cloudpasture.common.ResultCode;
import com.cloudpasture.dto.AddressDTO;
import com.cloudpasture.entity.Address;
import com.cloudpasture.mapper.AddressMapper;
import com.cloudpasture.service.AddressService;
import com.cloudpasture.vo.AddressVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户地址表 服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@Service
public class AddressServiceImpl extends ServiceImpl<AddressMapper, Address> implements AddressService {

    @Override
    @Transactional
    public AddressVO createAddress(Long userId, AddressDTO addressDTO) {
        // 创建地址对象
        Address address = new Address();
        BeanUtils.copyProperties(addressDTO, address);
        address.setUserId(userId);
        address.setIsDefault(addressDTO.getIsDefault() != null && addressDTO.getIsDefault() ? 1 : 0);
        address.setCreateTime(LocalDateTime.now());
        address.setUpdateTime(LocalDateTime.now());

        // 如果设置为默认地址，先清除其他默认地址
        if (address.getIsDefault() == 1) {
            baseMapper.clearDefaultByUserId(userId);
        }

        // 保存地址
        boolean success = save(address);
        if (!success) {
            throw new BusinessException(ResultCode.ERROR, "地址创建失败");
        }

        log.info("用户{}创建地址成功，地址ID：{}", userId, address.getId());
        return convertToVO(address);
    }

    @Override
    @Transactional
    public AddressVO updateAddress(Long userId, Long addressId, AddressDTO addressDTO) {
        // 查询地址是否存在且属于当前用户
        Address address = getById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.ERROR, "地址不存在或无权限");
        }

        // 更新地址信息
        BeanUtils.copyProperties(addressDTO, address);
        address.setIsDefault(addressDTO.getIsDefault() != null && addressDTO.getIsDefault() ? 1 : 0);
        address.setUpdateTime(LocalDateTime.now());

        // 如果设置为默认地址，先清除其他默认地址
        if (address.getIsDefault() == 1) {
            baseMapper.clearDefaultByUserId(userId);
        }

        // 更新地址
        boolean success = updateById(address);
        if (!success) {
            throw new BusinessException(ResultCode.ERROR, "地址更新失败");
        }

        log.info("用户{}更新地址成功，地址ID：{}", userId, addressId);
        return convertToVO(address);
    }

    @Override
    public boolean deleteAddress(Long userId, Long addressId) {
        // 查询地址是否存在且属于当前用户
        Address address = getById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.ERROR, "地址不存在或无权限");
        }

        // 删除地址
        boolean success = removeById(addressId);
        if (success) {
            log.info("用户{}删除地址成功，地址ID：{}", userId, addressId);
        }
        return success;
    }

    @Override
    public List<AddressVO> getUserAddresses(Long userId) {
        List<Address> addresses = baseMapper.selectByUserId(userId);
        return addresses.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public AddressVO getDefaultAddress(Long userId) {
        Address address = baseMapper.selectDefaultByUserId(userId);
        return address != null ? convertToVO(address) : null;
    }

    @Override
    @Transactional
    public boolean setDefaultAddress(Long userId, Long addressId) {
        // 查询地址是否存在且属于当前用户
        Address address = getById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.ERROR, "地址不存在或无权限");
        }

        // 先清除所有默认地址
        baseMapper.clearDefaultByUserId(userId);
        
        // 设置新的默认地址
        int result = baseMapper.setDefaultAddress(addressId, userId);
        if (result > 0) {
            log.info("用户{}设置默认地址成功，地址ID：{}", userId, addressId);
        }
        return result > 0;
    }

    @Override
    public AddressVO getAddressById(Long userId, Long addressId) {
        Address address = getById(addressId);
        if (address == null || !address.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.ERROR, "地址不存在或无权限");
        }
        return convertToVO(address);
    }

    @Override
    public AddressVO convertToVO(Address address) {
        if (address == null) {
            return null;
        }

        AddressVO vo = new AddressVO();
        BeanUtils.copyProperties(address, vo);
        vo.setIsDefault(address.getIsDefault() == 1);
        vo.setFullAddress(address.getProvince() + address.getCity() + address.getDetailAddress());
        return vo;
    }
}
