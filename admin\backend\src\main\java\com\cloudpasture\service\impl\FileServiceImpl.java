package com.cloudpasture.service.impl;

import com.cloudpasture.common.BusinessException;
import com.cloudpasture.common.ResultCode;
import com.cloudpasture.service.FileService;
import com.cloudpasture.utils.CosUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.UUID;

/**
 * 文件服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Autowired
    private CosUtils cosUtils;

    // 支持的图片类型
    private static final String[] IMAGE_TYPES = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
    
    // 最大文件大小：10MB
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;

    @Override
    public String uploadFile(MultipartFile file, String folder) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }

        // 检查文件大小
        if (!isValidFileSize(file, MAX_FILE_SIZE)) {
            throw new BusinessException(ResultCode.FILE_SIZE_ERROR);
        }

        try {
            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = folder + "/" + UUID.randomUUID().toString() + extension;

            // 上传到COS
            String fileUrl = cosUtils.uploadFile(file.getInputStream(), fileName, file.getContentType());
            
            log.info("文件上传成功，文件名：{}，URL：{}", fileName, fileUrl);
            return fileUrl;
        } catch (Exception e) {
            log.error("文件上传失败：", e);
            throw new BusinessException(ResultCode.FILE_UPLOAD_ERROR);
        }
    }

    @Override
    public String uploadImage(MultipartFile file) {
        // 检查文件类型
        if (!isValidFileType(file, IMAGE_TYPES)) {
            throw new BusinessException(ResultCode.FILE_TYPE_ERROR);
        }

        return uploadFile(file, "images");
    }

    @Override
    public boolean deleteFile(String fileUrl) {
        try {
            // 从URL中提取文件名
            String fileName = extractFileNameFromUrl(fileUrl);
            boolean success = cosUtils.deleteFile(fileName);
            
            if (success) {
                log.info("文件删除成功，文件名：{}", fileName);
            } else {
                log.error("文件删除失败，文件名：{}", fileName);
            }
            
            return success;
        } catch (Exception e) {
            log.error("文件删除失败：", e);
            return false;
        }
    }

    @Override
    public String getFileUrl(String fileName) {
        return cosUtils.getFileUrl(fileName);
    }

    @Override
    public boolean isValidFileType(MultipartFile file, String[] allowedTypes) {
        if (file == null || file.getOriginalFilename() == null) {
            return false;
        }

        String originalFilename = file.getOriginalFilename().toLowerCase();
        String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        
        return Arrays.asList(allowedTypes).contains(extension);
    }

    @Override
    public boolean isValidFileSize(MultipartFile file, long maxSize) {
        return file != null && file.getSize() <= maxSize;
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String fileUrl) {
        if (fileUrl == null || fileUrl.isEmpty()) {
            throw new BusinessException("文件URL不能为空");
        }

        // 假设URL格式为：https://bucket.cos.region.myqcloud.com/folder/filename.ext
        int lastSlashIndex = fileUrl.lastIndexOf("/");
        if (lastSlashIndex == -1) {
            throw new BusinessException("无效的文件URL");
        }

        return fileUrl.substring(lastSlashIndex + 1);
    }
}
