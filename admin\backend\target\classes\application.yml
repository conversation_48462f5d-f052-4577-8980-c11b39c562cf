server:
  port: 8080
  servlet:
    context-path: /api

spring:
  profiles:
    active: default
  application:
    name: cloud-pasture-mall
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************************************
    username: root
    password: xiaoma
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  # 解决 Springfox 与 Spring Boot 2.6+ 兼容性问题
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# 日志配置
logging:
  level:
    com.cloudpasture.mapper: debug
    root: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# JWT配置
jwt:
  secret: cloudpasture2025
  expiration: 604800

# 微信小程序配置
wechat:
  miniapp:
    app-id: wxa179c4885f1074a2
    app-secret: 056cf1108a80a4fa2a9ae2ab57c41958
  # 微信支付配置
  appId: wxa179c4885f1074a2 
  mchId: 1724600487       
  apiKey: AbCd1234EfGh5678IjKl9012MnOp3456  # 微信支付API密钥（请替换为实际密钥）
  notifyUrl: http://localhost:8080/api/payment/wechat/notify  # 支付回调通知地址

# 腾讯云COS配置
cos:
  secret-id: AKIDGNHxPddR2PPQEqEOL5btzvfBUZR6XkkX
  secret-key: sb9g3DA0rbLwLYlT0KQOMCtUuAQWO3bP
  region: ap-guangzhou
  bucket-name: yutingnongchang-1369375721
  base-url: https://yutingnongchang-1369375721.cos.ap-guangzhou.myqcloud.com
  # CDN加速域名
  cdn-domain: https://cos.uuutoo.cn

# Knife4j配置
knife4j:
  enable: true
  openapi:
    title: 愉汀农场小程序API文档
    description: 愉汀农场小程序后端API接口文档
    version: 1.0.0
    concat: 开发团队
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
    enable-reload-cache-parameter: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false
    enable-host-text: localhost:8080
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
    enable-search: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: Apache License 2.0 | Copyright 2025-[愉汀农场小程序]
