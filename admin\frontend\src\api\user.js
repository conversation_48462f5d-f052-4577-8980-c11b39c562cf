import request from '@/utils/request'

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/user/list',
    method: 'get',
    params
  })
}

// 根据ID获取用户详情
export function getUserById(id) {
  return request({
    url: `/user/${id}`,
    method: 'get'
  })
}

// 更新用户信息
export function updateUserInfo(data) {
  return request({
    url: '/user/update-info',
    method: 'put',
    params: data
  })
}

// 获取用户统计信息
export function getUserStats() {
  return request({
    url: '/user/stats',
    method: 'get'
  })
}

// 删除用户（管理员功能）
export function deleteUser(id) {
  return request({
    url: `/user/${id}`,
    method: 'delete'
  })
}

// 批量删除用户
export function batchDeleteUsers(ids) {
  return request({
    url: '/user/batch-delete',
    method: 'delete',
    data: { ids }
  })
}

// 重置用户密码
export function resetUserPassword(id, newPassword) {
  return request({
    url: `/user/${id}/reset-password`,
    method: 'put',
    data: { newPassword }
  })
}

// 更新用户状态
export function updateUserStatus(id, status) {
  return request({
    url: `/user/${id}/status`,
    method: 'put',
    params: { status }
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/user/create',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/user/${id}`,
    method: 'put',
    data
  })
}
