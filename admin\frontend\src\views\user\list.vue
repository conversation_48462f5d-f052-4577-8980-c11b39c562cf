<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入昵称或手机号"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>

    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" prop="id" sortable="custom" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="头像" width="80px" align="center">
        <template slot-scope="{row}">
          <el-avatar :src="row.avatarUrl" :size="40">
            <i class="el-icon-user-solid"></i>
          </el-avatar>
        </template>
      </el-table-column>
      <el-table-column label="昵称" min-width="120px">
        <template slot-scope="{row}">
          <span>{{ row.nickname }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手机号" min-width="120px">
        <template slot-scope="{row}">
          <span>{{ row.phone }}</span>
        </template>
      </el-table-column>
      <el-table-column label="积分" width="100px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.points }}</span>
        </template>
      </el-table-column>
      <el-table-column label="微信OpenID" min-width="200px">
        <template slot-scope="{row}">
          <span>{{ row.wechatOpenid || '未绑定' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" width="150px" align="center">
        <template slot-scope="{row}">
          <span>{{ formatDateTime(row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="{row,$index}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button
            size="mini"
            type="info"
            @click="handleViewDetail(row)"
          >
            详情
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(row, $index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 编辑用户弹窗 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '添加用户' : '编辑用户'"
      :visible.sync="dialogFormVisible"
      width="500px"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="80px"
        style="width: 400px; margin-left:50px;"
      >
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="temp.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="头像URL" prop="avatarUrl">
          <el-input v-model="temp.avatarUrl" placeholder="请输入头像URL" />
        </el-form-item>
        <el-alert
          title="注意：手机号和积分等其他信息暂不支持在此处修改"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;">
        </el-alert>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 用户详情弹窗 -->
    <el-dialog title="用户详情" :visible.sync="detailDialogVisible" width="500px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="用户ID">{{ userDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="昵称">{{ userDetail.nickname }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ userDetail.phone }}</el-descriptions-item>
        <el-descriptions-item label="微信OpenID">{{ userDetail.wechatOpenid || '未绑定' }}</el-descriptions-item>
        <el-descriptions-item label="积分">{{ userDetail.points }}</el-descriptions-item>
        <el-descriptions-item label="注册时间">{{ formatDateTime(userDetail.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDateTime(userDetail.updateTime) }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserList, deleteUser, getUserById, updateUserInfo } from '@/api/user'
import waves from '@/directive/waves' // waves directive
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
  name: 'UserList',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        keyword: undefined
      },
      dialogFormVisible: false,
      detailDialogVisible: false,
      dialogStatus: '',
      temp: {
        id: undefined,
        nickname: '',
        avatarUrl: ''
      },
      userDetail: {},
      rules: {
        nickname: [{ required: true, message: '昵称不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      try {
        // 处理ISO格式时间字符串 "2025-08-10T18:22:07"
        const date = new Date(dateTime)
        if (isNaN(date.getTime())) return '-'

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}`
      } catch (error) {
        console.error('时间格式化失败:', error)
        return '-'
      }
    },
    getList() {
      this.listLoading = true
      getUserList(this.listQuery).then(response => {
        this.list = response.data.records || response.data.items || []
        this.total = response.data.total || 0
        this.listLoading = false
      }).catch(error => {
        console.error('获取用户列表失败:', error)
        this.$message.error('获取用户列表失败')
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        nickname: '',
        avatarUrl: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleViewDetail(row) {
      this.userDetail = Object.assign({}, row)
      this.detailDialogVisible = true
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$message.info('创建用户功能暂未实现')
          this.dialogFormVisible = false
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 只发送后端支持的字段
          const updateParams = {
            nickname: this.temp.nickname,
            avatarUrl: this.temp.avatarUrl
          }
          updateUserInfo(updateParams).then(() => {
            const index = this.list.findIndex(v => v.id === this.temp.id)
            // 更新列表中的数据
            this.list.splice(index, 1, { ...this.list[index], ...updateParams })
            this.dialogFormVisible = false
            this.$message({
              message: '更新成功',
              type: 'success'
            })
          }).catch(error => {
            console.error('更新用户失败:', error)
            this.$message.error('更新用户失败: ' + (error.response?.data?.message || error.message))
          })
        }
      })
    },
    handleDelete(row, index) {
      this.$confirm('确定要删除该用户吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteUser(row.id).then(() => {
          this.list.splice(index, 1)
          this.total--
          this.$message({
            message: '删除成功',
            type: 'success'
          })
        }).catch(error => {
          console.error('删除用户失败:', error)
          this.$message.error('删除用户失败')
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  padding-bottom: 10px;
  
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
    margin-right: 10px;
  }
}
</style>
