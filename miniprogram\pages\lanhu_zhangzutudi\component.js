Component({
  properties: {},
  data: {
    // ========== 虚拟数据区域 - 后续替换为API调用 ==========
    landList: [], // 土地数据列表，将在页面加载时填充
    availableLands: [], // 可租赁土地列表
    reservedLands: [], // 已预定土地列表
    countdownDisplay: { hours: '00', minutes: '00', seconds: '00' }, // 倒计时显示
    countdownTimer: null, // 倒计时定时器

    // 虚拟土地数据 - TODO: 替换为API返回数据
    mockLandData: [
      {
        id: 1,
        status: 'available', // available: 可租赁, reserved: 已预定, sold: 已售出
        location: '河南省信阳市',
        title: '种植土地【高产xxx】',
        type: '水稻地【每年高产xx】',
        price: 2899,
        originalPrice: 3699,
        discount: '7折',
        discountLabel: '今日折扣',
        countdown: 1501, // 剩余秒数
        image: '../../images/lanhu_zhangzutudi/FigmaDDSSlicePNGb5658093b478b7853a9f72f389afe148.png',
        tags: ['优质土地', '限时秒杀'],
        area: '0.5亩',
        description: '高产优质土地，适合种植多种农作物'
      },
      {
        id: 2,
        status: 'available',
        location: '山东省济南市',
        title: '种植高产农作物',
        type: '综合种植地',
        price: 199,
        originalPrice: 420,
        discount: '5折',
        discountLabel: '限时秒杀',
        countdown: 2301,
        image: '../../images/lanhu_zhangzutudi/FigmaDDSSlicePNGdd110f1df0677f042b2a6a2d476d82a9.png',
        tags: ['优质土地', '限时秒杀'],
        area: '0.8亩',
        description: '适合种植高产农作物的优质土地'
      },
      {
        id: 3,
        status: 'reserved',
        location: '湖北省武汉市',
        title: '水稻+麦子结合地',
        type: '复合种植地',
        price: 199,
        originalPrice: 420,
        discount: '5折',
        discountLabel: '已结束',
        countdown: 0,
        image: '../../images/lanhu_zhangzutudi/FigmaDDSSlicePNGb5658093b478b7853a9f72f389afe148.png',
        tags: ['已预定'],
        area: '1.2亩',
        description: '水稻和麦子轮作的复合种植土地',
        reservedTime: '2024-01-15 14:30:00'
      },
      {
        id: 4,
        status: 'reserved',
        location: '江苏省南京市',
        title: '有机蔬菜种植地',
        type: '有机农业用地',
        price: 350,
        originalPrice: 580,
        discount: '6折',
        discountLabel: '已结束',
        countdown: 0,
        image: '../../images/lanhu_zhangzutudi/FigmaDDSSlicePNGdd110f1df0677f042b2a6a2d476d82a9.png',
        tags: ['已预定'],
        area: '0.6亩',
        description: '专门用于有机蔬菜种植的认证土地',
        reservedTime: '2024-01-14 09:15:00'
      }
    ]
    // ========== 虚拟数据区域结束 ==========
  },

  lifetimes: {
    created: function () {},
    attached: function () {
      console.info("长租土地页面加载");
      console.log("初始数据:", this.data);
      this.loadLandData();
      this.startCountdown();
    },
    detached: function () {
      console.info("长租土地页面卸载");
      this.stopCountdown();
    },
  },

  methods: {
    // ========== API预留接口区域 ==========
    /**
     * 加载土地数据 - API调用预留接口
     * TODO: 替换为真实API调用
     */
    loadLandData: function() {
      try {
        // TODO: 替换为真实API调用
        // const response = await wx.request({
        //   url: 'https://api.example.com/lands',
        //   method: 'GET',
        //   header: {
        //     'Authorization': 'Bearer ' + wx.getStorageSync('token')
        //   }
        // });
        // this.setData({
        //   landList: response.data.lands
        // });

        // 临时使用虚拟数据
        const landList = this.data.mockLandData;
        const availableLands = landList.filter(land => land.status === 'available');
        const reservedLands = landList.filter(land => land.status === 'reserved');

        // 格式化第一个可租赁土地的倒计时
        let countdownDisplay = { hours: '00', minutes: '00', seconds: '00' };
        if (availableLands.length > 0) {
          countdownDisplay = this.formatCountdown(availableLands[0].countdown);
        }

        this.setData({
          landList: landList,
          availableLands: availableLands,
          reservedLands: reservedLands,
          countdownDisplay: countdownDisplay
        });

        console.log('土地数据加载完成:', this.data.landList);
        console.log('可租赁土地数量:', this.data.availableLands.length);
        console.log('已预定土地数量:', this.data.reservedLands.length);
        console.log('倒计时显示:', this.data.countdownDisplay);

        // 验证数据加载
        if (this.data.landList.length > 0) {
          wx.showToast({
            title: `加载了${this.data.landList.length}块土地`,
            icon: 'success',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('加载土地数据失败:', error);
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        });
      }
    },

    /**
     * 更新土地状态 - API调用预留接口
     * @param {number} landId 土地ID
     * @param {string} newStatus 新状态
     * TODO: 替换为真实API调用
     */
    updateLandStatus: function(landId, newStatus) {
      try {
        // TODO: 替换为真实API调用
        // await wx.request({
        //   url: `https://api.example.com/lands/${landId}/status`,
        //   method: 'PUT',
        //   data: { status: newStatus },
        //   header: {
        //     'Authorization': 'Bearer ' + wx.getStorageSync('token')
        //   }
        // });

        // 临时更新本地数据
        const landList = this.data.landList.map(land => {
          if (land.id === landId) {
            return {
              ...land,
              status: newStatus,
              countdown: newStatus === 'reserved' ? 0 : land.countdown,
              reservedTime: newStatus === 'reserved' ? new Date().toLocaleString() : land.reservedTime
            };
          }
          return land;
        });

        this.setData({
          landList,
          availableLands: landList.filter(land => land.status === 'available'),
          reservedLands: landList.filter(land => land.status === 'reserved')
        });
        console.log(`土地 ${landId} 状态更新为: ${newStatus}`);
      } catch (error) {
        console.error('更新土地状态失败:', error);
      }
    },
    // ========== API预留接口区域结束 ==========

    // ========== 倒计时功能区域 ==========
    /**
     * 启动倒计时
     */
    startCountdown: function() {
      this.data.countdownTimer = setInterval(() => {
        this.updateCountdown();
      }, 1000);
    },

    /**
     * 停止倒计时
     */
    stopCountdown: function() {
      if (this.data.countdownTimer) {
        clearInterval(this.data.countdownTimer);
        this.data.countdownTimer = null;
      }
    },

    /**
     * 更新倒计时
     */
    updateCountdown: function() {
      const landList = this.data.landList.map(land => {
        if (land.status === 'available' && land.countdown > 0) {
          const newCountdown = land.countdown - 1;

          // 倒计时结束，自动变为已预定状态
          if (newCountdown <= 0) {
            console.log(`土地 ${land.id} 倒计时结束，自动变为已预定状态`);
            this.updateLandStatus(land.id, 'reserved');
            return {
              ...land,
              countdown: 0,
              status: 'reserved',
              discountLabel: '已结束',
              tags: ['已预定']
            };
          }

          return { ...land, countdown: newCountdown };
        }
        return land;
      });

      const availableLands = landList.filter(land => land.status === 'available');
      const reservedLands = landList.filter(land => land.status === 'reserved');

      // 更新倒计时显示
      let countdownDisplay = { hours: '00', minutes: '00', seconds: '00' };
      if (availableLands.length > 0) {
        countdownDisplay = this.formatCountdown(availableLands[0].countdown);
      }

      this.setData({
        landList,
        availableLands: availableLands,
        reservedLands: reservedLands,
        countdownDisplay: countdownDisplay
      });
    },

    /**
     * 格式化倒计时显示
     * @param {number} seconds 秒数
     * @returns {object} 格式化后的时间对象
     */
    formatCountdown: function(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;

      return {
        hours: hours.toString().padStart(2, '0'),
        minutes: minutes.toString().padStart(2, '0'),
        seconds: secs.toString().padStart(2, '0')
      };
    },
    // ========== 倒计时功能区域结束 ==========

    // ========== 业务逻辑方法区域 ==========
    /**
     * 获取可租赁的土地列表
     */
    getAvailableLands: function() {
      return this.data.landList.filter(land => land.status === 'available');
    },

    /**
     * 获取已预定的土地列表
     */
    getReservedLands: function() {
      return this.data.landList.filter(land => land.status === 'reserved');
    },

    /**
     * 根据ID获取土地信息
     * @param {number} landId 土地ID
     */
    getLandById: function(landId) {
      return this.data.landList.find(land => land.id === landId);
    },

    /**
     * 检查土地是否可租赁
     * @param {number} landId 土地ID
     */
    isLandAvailable: function(landId) {
      const land = this.getLandById(landId);
      return land && land.status === 'available' && land.countdown > 0;
    },

    /**
     * 土地详情点击事件
     * @param {object} e 事件对象
     */
    onLandDetailTap: function(e) {
      const landId = e.currentTarget.dataset.landId;
      const land = this.getLandById(landId);

      if (!land) {
        wx.showToast({
          title: '土地信息不存在',
          icon: 'none'
        });
        return;
      }

      console.log('查看土地详情:', land);

      // TODO: 跳转到土地详情页面
      // wx.navigateTo({
      //   url: `/pages/land_detail/component?landId=${landId}`
      // });

      // 临时显示土地信息
      wx.showModal({
        title: '土地详情',
        content: `位置: ${land.location}\n类型: ${land.type}\n面积: ${land.area}\n价格: ¥${land.price}\n状态: ${land.status === 'available' ? '可租赁' : '已预定'}`,
        showCancel: false
      });
    },

    /**
     * 立即参与/去看看按钮点击事件
     * @param {object} e 事件对象
     */
    onParticipateClick: function(e) {
      const landId = e.currentTarget.dataset.landId;
      const land = this.getLandById(landId);

      if (!land) {
        wx.showToast({
          title: '土地信息不存在',
          icon: 'none'
        });
        return;
      }

      if (land.status !== 'available') {
        wx.showToast({
          title: '该土地已被预定',
          icon: 'none'
        });
        return;
      }

      if (land.countdown <= 0) {
        wx.showToast({
          title: '活动已结束',
          icon: 'none'
        });
        return;
      }

      console.log('参与土地租赁:', land);

      // TODO: 跳转到租赁流程页面
      // wx.navigateTo({
      //   url: `/pages/land_rental/component?landId=${landId}`
      // });

      // 临时模拟租赁流程
      wx.showModal({
        title: '确认租赁',
        content: `确定要租赁 ${land.location} 的土地吗？\n价格: ¥${land.price}`,
        success: (res) => {
          if (res.confirm) {
            this.simulateRental(landId);
          }
        }
      });
    },

    /**
     * 模拟租赁流程 - 临时方法
     * @param {number} landId 土地ID
     */
    simulateRental: function(landId) {
      // 模拟租赁成功，更新土地状态
      this.updateLandStatus(landId, 'reserved');

      wx.showToast({
        title: '租赁成功！',
        icon: 'success'
      });

      // TODO: 跳转到支付页面或订单页面
      console.log('租赁流程完成，土地ID:', landId);
    }
    // ========== 业务逻辑方法区域结束 ==========
  },
});
