<view class="page">
  <view class="block_1">
    <!-- 可租赁土地区域 -->
    <view class="section_3">
      <!-- 未被抢走标题和倒计时 -->
      <view class="group_4">
        <view class="box_1">
        </view>
        <text lines="1" class="text_2">未被抢走</text>
      </view>

      <!-- 可租赁土地列表 -->
      <view class="group_6">
        <!--未被抢走列表总界面-->
        <block wx:for="{{availableLands}}" wx:key="id" wx:for-item="land">
          <!-- 土地卡片 - 样式1 (偶数索引) -->
          <view class="box_2" wx:if="{{index % 2 === 0}}" bindtap="onLandDetailTap" data-land-id="{{land.id}}">
            <view class="box_3">
            </view>
            <view class="box_5">
              <text lines="1" class="text_9">{{land.type}}</text>

              <view class="section_4">
                <view class="text-wrapper_4">
                  <text lines="1" class="text_10">{{land.discount}}</text>
                </view>
                <view class="text-wrapper_5">
                  <text lines="1" class="text_11">{{land.discountLabel}}</text>
                </view>
              </view>
              <view class="section_5">
                <text lines="1" class="text_12">到手价</text>
                <text lines="1" class="text_13">￥</text>
                <text lines="1" class="text_14">{{land.price}}</text>
                <view class="group_8" bindtap="onParticipateClick" data-land-id="{{land.id}}">
                  <text lines="1" class="text_15">去看看</text>
                  <view class="group_10"></view>
                </view>
              </view>
              <view class="text-wrapper_6">
                <text lines="1" class="text_16">市场参考价：</text>
                <text lines="1" class="text_17">￥{{land.originalPrice}}</text>
              </view>
            </view>
          </view>
          <!-- 土地卡片 - 样式2 (奇数索引) -->
          <view wx:else class="box_2" bindtap="onLandDetailTap" data-land-id="{{land.id}}">
            <image src="{{land.image}}" class="image_1"></image>
            <view class="text-group_1">
              <text lines="1" class="text_18">{{land.location}}{{land.title}}</text>
              <view class="text-wrapper_7">
                <text lines="1" class="text_19">到手价</text>
                <text lines="1" class="text_20">￥</text>
                <text lines="1" class="text_21">{{land.price}}</text>
              </view>
              <view class="text-wrapper_8">
                <text lines="1" class="text_22">市场参考价：</text>
                <text lines="1" class="text_23">￥{{land.originalPrice}}</text>
              </view>
              <view class="text-wrapper_9">
                <text lines="1" class="text_24">{{land.discountLabel}}</text>
              </view>

              <view class="group_11" bindtap="onParticipateClick" data-land-id="{{land.id}}">
                <text lines="1" class="text_25">去看看</text>
              </view>
            </view>

          </view>
        </block>

        <!-- 无可租赁土地时的提示 -->
        <view wx:if="{{availableLands.length === 0}}" class="no-available-lands">
          <text class="no-lands-text">暂无可租赁土地</text>
          <text class="no-lands-desc">请关注后续土地上架信息</text>
        </view>
      </view>

      <!-- 已预定土地区域 -->
      <view class="group_12">
        <view class="box_1">
          <view class="section_6"></view>
        </view>
        <text lines="1" class="text_27">已被预定</text>
      </view>

      <!-- 已预定土地列表 -->
      <block wx:for="{{reservedLands}}" wx:key="id" wx:for-item="land">
        <!-- 已预定土地卡片 - 样式1 (偶数索引) -->
        <view wx:if="{{index % 2 === 0}}" class="group_13">
          <view class="box_10">
            <view class="group_14"></view>
          </view>
          <view class="box_11 reserved-land">
            <text lines="1" class="text_28">{{land.location}}{{land.title}}</text>
            <view class="group_15">
              <view class="text-wrapper_11 reserved-tag">
                <text lines="1" class="text_29">{{land.tags[0] || '已预定'}}</text>
              </view>
              <view class="text-wrapper_12 reserved-label">
                <text lines="1" class="text_30">{{land.discountLabel}}</text>
              </view>
            </view>
            <view class="group_16">
              <text lines="1" class="text_31 reserved-price-label">原价</text>
              <text lines="1" class="text_32 reserved-price">￥</text>
              <text lines="1" class="text_33 reserved-price">{{land.price}}</text>
              <text lines="1" class="text_34 reserved-original-price">￥{{land.originalPrice}}</text>
              <view class="text-wrapper_13 reserved-btn">
                <text lines="1" class="text_35">已售罄</text>
              </view>
            </view>
            <view wx:if="{{land.reservedTime}}" class="reserved-time">
              <text class="reserved-time-text">预定时间: {{land.reservedTime}}</text>
            </view>
          </view>

        </view>

        <!-- 已预定土地卡片 - 样式2 (奇数索引) -->
        <view wx:else class="group_17">
          <view class="image-text_2 reserved-land">
            <image src="{{land.image}}" class="image_2 reserved-image"></image>
            <text lines="1" class="text_36">{{land.location}}{{land.title}}</text>
            <view class="text-wrapper_14">
              <text lines="1" class="text_37 reserved-price-label">原价</text>
              <text lines="1" class="text_38 reserved-price">￥</text>
              <text lines="1" class="text_39 reserved-price">{{land.price}}</text>
            </view>

            <view class="reserverp">
              <text lines="1" class="text_40">{{land.discountLabel}}</text>
            </view>
            <view class="text-wrapper_16 reserved-btn">
              <text lines="1" class="text_41">已售罄</text>
            </view>
            <view class="text-wrapper_17 reserved-tag">
              <text lines="1" class="text_42">{{land.tags[0] || '已预定'}}</text>
            </view>
            <view wx:if="{{land.reservedTime}}" class="reserved-time">
              <text class="reserved-time-text">预定时间: {{land.reservedTime}}</text>
            </view>
          </view>
        </view>
      </block>

      <!-- 无已预定土地时的提示 -->
      <view wx:if="{{reservedLands.length === 0}}" class="no-reserved-lands">
        <text class="no-lands-text">暂无已预定土地</text>
      </view>
    </view>
  </view>
</view>